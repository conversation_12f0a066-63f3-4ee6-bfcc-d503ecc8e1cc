import { apiClient as apiRequest } from '@/shared/api/';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';
import {
  BusinessApiService,
  ApiUserOrderListItem,
  ApiUserOrderStatusResponse,
  OrderQueryParams as ApiOrderQueryParams,
  CreateUserOrderDto as ApiCreateUserOrderDto,
  OrderStatusEnum,
  PaymentStatusEnum,
  PaymentMethodEnum
} from './business-api.service';

// All imports above are used in this file

/**
 * Enum cho trạng thái đơn hàng (legacy - sử dụng OrderStatusEnum từ business-api.service)
 * @deprecated Use OrderStatusEnum from business-api.service instead
 */
export enum OrderStatus {
  PENDING = 'PENDING',
  PROCESSING = 'PROCESSING',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED',
  REFUNDED = 'REFUNDED',
}

/**
 * Enum cho phương thức thanh toán (legacy - sử dụng PaymentMethodEnum từ business-api.service)
 * @deprecated Use PaymentMethodEnum from business-api.service instead
 */
export enum PaymentMethod {
  CASH = 'CASH',
  CREDIT_CARD = 'CREDIT_CARD',
  BANK_TRANSFER = 'BANK_TRANSFER',
  DIGITAL_WALLET = 'DIGITAL_WALLET',
}

/**
 * Interface cho thông tin khách hàng
 */
export interface CustomerInfo {
  id: number;
  name: string;
  email: string;
  phone: string;
  address?: string;
}

/**
 * Interface cho sản phẩm trong đơn hàng
 */
export interface OrderItem {
  id: number;
  productId: number;
  productName: string;
  quantity: number;
  price: number;
  totalPrice: number;
}

/**
 * Interface cho đơn hàng
 */
export interface Order {
  id: number;
  orderNumber: string;
  customerId: number;
  customerInfo: CustomerInfo;
  items: OrderItem[];
  totalAmount: number;
  status: OrderStatus;
  paymentMethod: PaymentMethod;
  paymentStatus: 'PAID' | 'UNPAID' | 'PARTIALLY_PAID';
  createdAt: number;
  updatedAt: number;
  notes?: string;
}

/**
 * Interface cho danh sách đơn hàng (dựa trên API thật)
 */
export interface OrderListItem {
  id: string;
  userConvertCustomer: {
    id: string;
    avatar?: string;
    name: string;
    email: {
      primary: string;
      secondary?: string;
    };
    phone: string;
    platform?: string;
    timezone?: string;
    createdAt: string;
    updatedAt: string;
    address?: string;
    metadata?: Record<string, unknown>;
  };
  billInfo: {
    total: number;
    subtotal: number;
    shippingFee: number;
    paymentMethod: string;
    paymentStatus: string;
    selectedCarrier?: string;
    shippingServiceType?: string;
  };
  shippingStatus: string;
  createdAt: string;
  source: string;
  orderStatus: string;
}

/**
 * Interface cho danh sách đơn hàng đã được transform
 */
export interface TransformedOrderListItem {
  id: string;
  orderNumber: string;
  customerName: string;
  totalAmount: number;
  status: OrderStatus;
  paymentStatus: 'PAID' | 'UNPAID' | 'PARTIALLY_PAID';
  shippingStatus: string;
  createdAt: number;
  source: string;
}

/**
 * Interface cho tham số truy vấn đơn hàng (legacy)
 * @deprecated Use ApiOrderQueryParams from business-api.service instead
 */
export interface OrderQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  status?: OrderStatus;
  paymentStatus?: 'PAID' | 'UNPAID' | 'PARTIALLY_PAID';
  fromDate?: string;
  toDate?: string;
  sortBy?: string;
  sortDirection?: 'ASC' | 'DESC';
  userConvertCustomerId?: string;
}

/**
 * Interface cho dữ liệu tạo đơn hàng
 */
export interface CreateOrderData {
  customerId: number;
  items: Array<{
    productId: number;
    quantity: number;
    price: number;
    productType?: import('../types/product.types').ProductTypeEnum;
  }>;
  paymentMethod: PaymentMethod;
  notes?: string;
  shipping?: {
    method: import('../types/order.types').ShippingMethod;
    serviceId?: number;
    serviceName?: string;
    fromAddress: import('../types/order.types').AddressDto;
    toAddress: import('../types/order.types').AddressDto;
    fee: number;
    estimatedDelivery?: string;
    note?: string;
  };
  digitalDelivery?: {
    method: import('../types/order.types').DigitalDeliveryMethod;
    recipient: string;
    message?: string;
    scheduledDelivery?: string;
  };
  payment?: {
    method: string;
    status: string;
    codAmount?: number;
  };
  tags?: string[];
}

/**
 * Interface cho dữ liệu cập nhật đơn hàng
 */
export interface UpdateOrderData {
  status?: OrderStatus;
  paymentStatus?: 'PAID' | 'UNPAID' | 'PARTIALLY_PAID';
  notes?: string;
}

/**
 * Hàm transform dữ liệu từ API thành format hiển thị (updated for new API)
 */
export const transformOrderListItem = (item: ApiUserOrderListItem): TransformedOrderListItem => {
  // Map payment status
  const getPaymentStatus = (paymentStatus: PaymentStatusEnum): 'PAID' | 'UNPAID' | 'PARTIALLY_PAID' => {
    switch (paymentStatus) {
      case PaymentStatusEnum.PAID:
        return 'PAID';
      case PaymentStatusEnum.PENDING:
      case PaymentStatusEnum.FAILED:
        return 'UNPAID';
      case PaymentStatusEnum.REFUNDED:
        return 'PARTIALLY_PAID';
      default:
        return 'UNPAID';
    }
  };

  // Map order status
  const getOrderStatus = (status: OrderStatusEnum): OrderStatus => {
    switch (status) {
      case OrderStatusEnum.PENDING:
        return OrderStatus.PENDING;
      case OrderStatusEnum.CONFIRMED:
      case OrderStatusEnum.PROCESSING:
        return OrderStatus.PROCESSING;
      case OrderStatusEnum.COMPLETED:
        return OrderStatus.COMPLETED;
      case OrderStatusEnum.CANCELLED:
        return OrderStatus.CANCELLED;
      default:
        return OrderStatus.PENDING;
    }
  };

  return {
    id: item.id.toString(),
    orderNumber: `#${item.id}`,
    customerName: item.userConvertCustomer?.name || `Khách hàng ${item.userConvertCustomer?.id}`,
    totalAmount: item.billInfo.total,
    status: getOrderStatus(item.orderStatus),
    paymentStatus: getPaymentStatus(item.billInfo.paymentStatus),
    shippingStatus: item.shippingStatus,
    createdAt: item.createdAt,
    source: item.source,
  };
};

/**
 * Service xử lý API liên quan đến đơn hàng (updated to use BusinessApiService)
 */
export const OrderService = {
  /**
   * Lấy danh sách đơn hàng
   * @param params Tham số truy vấn
   * @returns Danh sách đơn hàng với phân trang
   */
  getOrders: async (params?: OrderQueryParams): Promise<ApiResponseDto<PaginatedResult<OrderListItem>>> => {
    // Transform legacy params to new API params
    const apiParams: ApiOrderQueryParams = {
      page: params?.page,
      limit: params?.limit,
      userConvertCustomerId: params?.userConvertCustomerId ? parseInt(params.userConvertCustomerId) : undefined,
      sortBy: params?.sortBy as 'createdAt' | 'updatedAt' | 'shippingStatus' | 'orderStatus',
      sortDirection: params?.sortDirection,
    };

    // Map legacy status to new enum
    if (params?.status) {
      switch (params.status) {
        case OrderStatus.PENDING:
          apiParams.orderStatus = OrderStatusEnum.PENDING;
          break;
        case OrderStatus.PROCESSING:
          apiParams.orderStatus = OrderStatusEnum.PROCESSING;
          break;
        case OrderStatus.COMPLETED:
          apiParams.orderStatus = OrderStatusEnum.COMPLETED;
          break;
        case OrderStatus.CANCELLED:
          apiParams.orderStatus = OrderStatusEnum.CANCELLED;
          break;
      }
    }

    const result = await BusinessApiService.getOrders(apiParams);

    // Transform to legacy format for backward compatibility
    return {
      result: result,
      message: 'Success',
      statusCode: 200,
      timestamp: Date.now(),
    } as ApiResponseDto<PaginatedResult<OrderListItem>>;
  },

  /**
   * Lấy chi tiết đơn hàng theo ID
   * @param id ID của đơn hàng
   * @returns Chi tiết đơn hàng
   */
  getOrderById: async (id: number): Promise<ApiResponseDto<Order>> => {
    const result = await BusinessApiService.getOrderDetail(id);

    // Transform to legacy format for backward compatibility
    return {
      result: result as unknown as Order,
      message: 'Success',
      statusCode: 200,
      timestamp: Date.now(),
    } as ApiResponseDto<Order>;
  },

  /**
   * Tạo đơn hàng mới
   * @param data Dữ liệu tạo đơn hàng
   * @returns Thông tin đơn hàng đã tạo
   */
  createOrder: async (data: CreateOrderData): Promise<ApiResponseDto<Order>> => {
    // Transform legacy data to new API format
    const apiData: ApiCreateUserOrderDto = {
      shopId: 1, // Default shop ID, should be configurable
      customerInfo: {
        customerId: data.customerId,
      },
      products: data.items.map(item => ({
        productId: item.productId,
        quantity: item.quantity,
      })),
      billInfo: {
        subtotal: data.items.reduce((sum, item) => sum + (item.price * item.quantity), 0),
        paymentMethod: data.paymentMethod as PaymentMethodEnum,
      },
      source: 'website',
      note: data.notes,
      tags: data.tags,
    };

    const result = await BusinessApiService.createOrder(apiData);

    // Transform to legacy format for backward compatibility
    return {
      result: result as unknown as Order,
      message: 'Success',
      statusCode: 201,
      timestamp: Date.now(),
    } as ApiResponseDto<Order>;
  },

  /**
   * Cập nhật đơn hàng
   * @param id ID của đơn hàng
   * @param data Dữ liệu cập nhật đơn hàng
   * @returns Thông tin đơn hàng đã cập nhật
   */
  updateOrder: async (id: number, data: UpdateOrderData): Promise<ApiResponseDto<Order>> => {
    // Note: Update API not implemented in BusinessApiService yet
    return apiRequest.put(`/user/orders/${id}`, data);
  },

  /**
   * Xóa đơn hàng
   * @param id ID của đơn hàng
   * @returns Thông báo xóa thành công
   */
  deleteOrder: async (id: number): Promise<ApiResponseDto<null>> => {
    // Note: Delete API not implemented in BusinessApiService yet
    return apiRequest.delete(`/user/orders/${id}`);
  },

  /**
   * Lấy thống kê trạng thái đơn hàng
   * @returns Thống kê trạng thái đơn hàng
   */
  getOrderStatusStats: async (): Promise<ApiResponseDto<ApiUserOrderStatusResponse>> => {
    const result = await BusinessApiService.getOrderStatusStats();

    return {
      result: result,
      message: 'Success',
      statusCode: 200,
      timestamp: Date.now(),
    } as ApiResponseDto<ApiUserOrderStatusResponse>;
  },

  /**
   * Tracking đơn hàng
   * @param id ID của đơn hàng
   * @returns Thông tin tracking
   */
  trackOrder: async (id: number): Promise<ApiResponseDto<unknown>> => {
    const result = await BusinessApiService.trackOrder(id);

    return {
      result: result,
      message: 'Success',
      statusCode: 200,
      timestamp: Date.now(),
    } as ApiResponseDto<unknown>;
  },
};
