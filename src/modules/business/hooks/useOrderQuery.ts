import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { OrderService, OrderQueryParams, CreateOrderData, UpdateOrderData, transformOrderListItem } from '../services/order.service';
import { NotificationUtil } from '@/shared/utils/notification';
import { AxiosError } from 'axios';
import { useTranslation } from 'react-i18next';

/**
 * Query keys cho order API
 */
export const ORDER_QUERY_KEYS = {
  all: ['business', 'orders'] as const,
  list: (params: OrderQueryParams) => [...ORDER_QUERY_KEYS.all, 'list', params] as const,
  detail: (id: number) => [...ORDER_QUERY_KEYS.all, 'detail', id] as const,
  statusStats: () => [...ORDER_QUERY_KEYS.all, 'status-stats'] as const,
  track: (id: number) => [...ORDER_QUERY_KEYS.all, 'track', id] as const,
};

/**
 * Hook lấy danh sách đơn hàng
 */
export const useOrders = (params: OrderQueryParams = {}) => {
  const { t } = useTranslation(['business', 'common']);

  return useQuery({
    queryKey: ORDER_QUERY_KEYS.list(params),
    queryFn: () => {
      console.log('🔍 [useOrders] Fetching orders with params:', params);
      return OrderService.getOrders(params);
    },
    select: (data) => {
      console.log('✅ [useOrders] Raw API response:', data);

      // Check if data structure is correct
      if (!data?.result?.items) {
        console.warn('⚠️ [useOrders] Invalid data structure:', data);
        return {
          items: [],
          meta: {
            currentPage: 1,
            totalItems: 0,
            totalPages: 0,
            itemsPerPage: 10,
          }
        };
      }

      // Transform dữ liệu từ API thành format hiển thị
      const transformedItems = data.result.items.map(transformOrderListItem);
      console.log('✅ [useOrders] Transformed items:', transformedItems);

      return {
        ...data.result,
        items: transformedItems,
      };
    },
    refetchOnWindowFocus: false,
    staleTime: 60000, // 60 seconds
    onError: (error: AxiosError<{ message: string }>) => {
      console.error('❌ [useOrders] Error fetching orders:', error);
      NotificationUtil.error({
        message: error.response?.data?.message || t('business:order.fetchError'),
        duration: 3000,
      });
    },
  });
};

/**
 * Hook để lấy chi tiết đơn hàng
 */
export const useOrder = (orderId: number) => {
  const { t } = useTranslation(['business', 'common']);

  const query = useQuery({
    queryKey: ORDER_QUERY_KEYS.detail(orderId),
    queryFn: () => OrderService.getOrderById(orderId),
    enabled: !!orderId && orderId > 0,
    refetchOnWindowFocus: false,
    staleTime: 300000, // 5 minutes
    onError: (error: AxiosError<{ message: string; errorCode?: string }>) => {
      console.error('❌ [useOrder] Error fetching order detail:', error);

      // Check if it's a permission error - don't show notification for these
      const status = error.response?.status;
      const errorMessage = error.response?.data?.message || '';
      const errorCode = error.response?.data?.errorCode || '';

      const isPermissionError = (status === 400 || status === 403) && (
        errorMessage.toLowerCase().includes('permission') ||
        errorMessage.toLowerCase().includes('access') ||
        errorMessage.toLowerCase().includes('unauthorized') ||
        errorMessage.toLowerCase().includes('forbidden') ||
        errorCode.includes('ACCESS_DENIED') ||
        errorCode.includes('PERMISSION')
      );

      if (!isPermissionError) {
        NotificationUtil.error({
          message: error.response?.data?.message || t('business:order.fetchDetailError'),
          duration: 3000,
        });
      } else {
        console.warn('⚠️ [useOrder] Permission error detected, not showing notification:', {
          status,
          message: errorMessage,
          errorCode
        });
      }
    },
  });

  return {
    ...query,
    order: query.data?.result,
  };
};

/**
 * Hook tạo đơn hàng mới
 */
export const useCreateOrder = () => {
  const queryClient = useQueryClient();
  const { t } = useTranslation(['business', 'common']);

  return useMutation({
    mutationFn: (data: CreateOrderData) => OrderService.createOrder(data),
    onSuccess: () => {
      NotificationUtil.success({
        message: t('business:order.createSuccess'),
        duration: 3000,
      });

      queryClient.invalidateQueries({
        queryKey: ORDER_QUERY_KEYS.all,
        exact: false,
      });
    },
    onError: (error: AxiosError<{ message: string }>) => {
      NotificationUtil.error({
        message: error.response?.data?.message || t('business:order.createError'),
        duration: 3000,
      });
    },
  });
};

/**
 * Hook cập nhật đơn hàng
 */
export const useUpdateOrder = () => {
  const queryClient = useQueryClient();
  const { t } = useTranslation(['business', 'common']);

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateOrderData }) =>
      OrderService.updateOrder(id, data),
    onSuccess: (_, variables) => {
      NotificationUtil.success({
        message: t('business:order.updateSuccess'),
        duration: 3000,
      });

      queryClient.invalidateQueries({
        queryKey: ORDER_QUERY_KEYS.detail(variables.id),
      });

      queryClient.invalidateQueries({
        queryKey: ORDER_QUERY_KEYS.all,
        exact: false,
      });
    },
    onError: (error: AxiosError<{ message: string }>) => {
      NotificationUtil.error({
        message: error.response?.data?.message || t('business:order.updateError'),
        duration: 3000,
      });
    },
  });
};

/**
 * Hook xóa đơn hàng
 */
export const useDeleteOrder = () => {
  const queryClient = useQueryClient();
  const { t } = useTranslation(['business', 'common']);

  return useMutation({
    mutationFn: (id: number) => OrderService.deleteOrder(id),
    onSuccess: () => {
      NotificationUtil.success({
        message: t('business:order.deleteSuccess'),
        duration: 3000,
      });

      queryClient.invalidateQueries({
        queryKey: ORDER_QUERY_KEYS.all,
        exact: false,
      });
    },
    onError: (error: AxiosError<{ message: string }>) => {
      NotificationUtil.error({
        message: error.response?.data?.message || t('business:order.deleteError'),
        duration: 3000,
      });
    },
  });
};

/**
 * Hook lấy danh sách đơn hàng của khách hàng cụ thể
 */
export const useCustomerOrders = (userConvertCustomerId: string, params: Omit<OrderQueryParams, 'userConvertCustomerId'> = {}) => {
  // t được sử dụng trong các thông báo lỗi ở các hook khác
  useTranslation(['business', 'common']);

  const queryParams = {
    ...params,
    userConvertCustomerId,
  };

  return useQuery({
    queryKey: ORDER_QUERY_KEYS.list(queryParams),
    queryFn: () => OrderService.getOrders(queryParams),
    select: (data) => {
      // Trả về dữ liệu thô từ API cho customer orders
      return data.result;
    },
    refetchOnWindowFocus: false,
    staleTime: 60000, // 60 seconds
    enabled: !!userConvertCustomerId, // Chỉ gọi API khi có userConvertCustomerId
  });
};



/**
 * Hook lấy thống kê trạng thái đơn hàng
 */
export const useOrderStatusStats = () => {
  const { t } = useTranslation(['business', 'common']);

  return useQuery({
    queryKey: ORDER_QUERY_KEYS.statusStats(),
    queryFn: () => OrderService.getOrderStatusStats(),
    select: (data) => data.result,
    refetchOnWindowFocus: false,
    staleTime: 300000, // 5 minutes
    onError: (error: AxiosError<{ message: string }>) => {
      NotificationUtil.error({
        message: error.response?.data?.message || t('business:order.statusStatsError'),
        duration: 3000,
      });
    },
  });
};

/**
 * Hook tracking đơn hàng
 */
export const useOrderTracking = (orderId: number) => {
  const { t } = useTranslation(['business', 'common']);

  return useQuery({
    queryKey: ORDER_QUERY_KEYS.track(orderId),
    queryFn: () => OrderService.trackOrder(orderId),
    select: (data) => data.result,
    enabled: !!orderId,
    refetchOnWindowFocus: false,
    staleTime: 60000, // 1 minute
    onError: (error: AxiosError<{ message: string }>) => {
      NotificationUtil.error({
        message: error.response?.data?.message || t('business:order.trackingError'),
        duration: 3000,
      });
    },
  });
};
