import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useOrderStatusStats } from './useOrderQuery';
import { OrderStatusEnum, ShippingStatusEnum } from '../types/order.types';

/**
 * Hook quản lý thống kê và trạng thái đơn hàng
 */
export const useOrderStatus = () => {
  const { t } = useTranslation(['business', 'common']);
  const { data: stats, isLoading, error } = useOrderStatusStats();

  // Tính toán các metrics tổng quan
  const metrics = useMemo(() => {
    if (!stats) return null;

    const orderStats = stats.orderStatus;
    const shippingStats = stats.shippingStatus;

    return {
      // Order metrics
      totalOrders: orderStats.total,
      pendingOrders: orderStats.pending,
      processingOrders: orderStats.confirmed + orderStats.processing,
      completedOrders: orderStats.completed,
      cancelledOrders: orderStats.cancelled,
      
      // Shipping metrics
      totalShippingOrders: shippingStats.total,
      pendingShipping: shippingStats.pending,
      inTransitShipping: shippingStats.preparing + shippingStats.shipped + shippingStats.inTransit + shippingStats.sorting,
      deliveredShipping: shippingStats.delivered,
      failedShipping: shippingStats.deliveryFailed + shippingStats.returning + shippingStats.cancelled,
      
      // Completion rates
      completionRate: orderStats.total > 0 ? (orderStats.completed / orderStats.total) * 100 : 0,
      deliveryRate: shippingStats.total > 0 ? (shippingStats.delivered / shippingStats.total) * 100 : 0,
      cancellationRate: orderStats.total > 0 ? (orderStats.cancelled / orderStats.total) * 100 : 0,
    };
  }, [stats]);

  // Cấu hình màu sắc cho các trạng thái
  const statusColors = useMemo(() => ({
    // Order status colors
    [OrderStatusEnum.PENDING]: '#f59e0b', // amber-500
    [OrderStatusEnum.CONFIRMED]: '#3b82f6', // blue-500
    [OrderStatusEnum.PROCESSING]: '#6366f1', // indigo-500
    [OrderStatusEnum.COMPLETED]: '#10b981', // emerald-500
    [OrderStatusEnum.CANCELLED]: '#ef4444', // red-500
    
    // Shipping status colors
    [ShippingStatusEnum.PENDING]: '#f59e0b', // amber-500
    [ShippingStatusEnum.PREPARING]: '#3b82f6', // blue-500
    [ShippingStatusEnum.SHIPPED]: '#6366f1', // indigo-500
    [ShippingStatusEnum.IN_TRANSIT]: '#8b5cf6', // violet-500
    [ShippingStatusEnum.SORTING]: '#06b6d4', // cyan-500
    [ShippingStatusEnum.DELIVERED]: '#10b981', // emerald-500
    [ShippingStatusEnum.DELIVERY_FAILED]: '#ef4444', // red-500
    [ShippingStatusEnum.RETURNING]: '#f97316', // orange-500
    [ShippingStatusEnum.CANCELLED]: '#6b7280', // gray-500
  }), []);

  // Dữ liệu cho biểu đồ đơn hàng
  const orderChartData = useMemo(() => {
    if (!stats) return [];

    return [
      {
        name: t('business:order.status.pending'),
        value: stats.orderStatus.pending,
        color: statusColors[OrderStatusEnum.PENDING],
      },
      {
        name: t('business:order.status.confirmed'),
        value: stats.orderStatus.confirmed,
        color: statusColors[OrderStatusEnum.CONFIRMED],
      },
      {
        name: t('business:order.status.processing'),
        value: stats.orderStatus.processing,
        color: statusColors[OrderStatusEnum.PROCESSING],
      },
      {
        name: t('business:order.status.completed'),
        value: stats.orderStatus.completed,
        color: statusColors[OrderStatusEnum.COMPLETED],
      },
      {
        name: t('business:order.status.cancelled'),
        value: stats.orderStatus.cancelled,
        color: statusColors[OrderStatusEnum.CANCELLED],
      },
    ].filter(item => item.value > 0);
  }, [stats, statusColors, t]);

  // Dữ liệu cho biểu đồ vận chuyển
  const shippingChartData = useMemo(() => {
    if (!stats) return [];

    return [
      {
        name: t('business:order.shippingStatus.pending'),
        value: stats.shippingStatus.pending,
        color: statusColors[ShippingStatusEnum.PENDING],
      },
      {
        name: t('business:order.shippingStatus.preparing'),
        value: stats.shippingStatus.preparing,
        color: statusColors[ShippingStatusEnum.PREPARING],
      },
      {
        name: t('business:order.shippingStatus.shipped'),
        value: stats.shippingStatus.shipped,
        color: statusColors[ShippingStatusEnum.SHIPPED],
      },
      {
        name: t('business:order.shippingStatus.inTransit'),
        value: stats.shippingStatus.inTransit,
        color: statusColors[ShippingStatusEnum.IN_TRANSIT],
      },
      {
        name: t('business:order.shippingStatus.sorting'),
        value: stats.shippingStatus.sorting,
        color: statusColors[ShippingStatusEnum.SORTING],
      },
      {
        name: t('business:order.shippingStatus.delivered'),
        value: stats.shippingStatus.delivered,
        color: statusColors[ShippingStatusEnum.DELIVERED],
      },
      {
        name: t('business:order.shippingStatus.deliveryFailed'),
        value: stats.shippingStatus.deliveryFailed,
        color: statusColors[ShippingStatusEnum.DELIVERY_FAILED],
      },
      {
        name: t('business:order.shippingStatus.returning'),
        value: stats.shippingStatus.returning,
        color: statusColors[ShippingStatusEnum.RETURNING],
      },
      {
        name: t('business:order.shippingStatus.cancelled'),
        value: stats.shippingStatus.cancelled,
        color: statusColors[ShippingStatusEnum.CANCELLED],
      },
    ].filter(item => item.value > 0);
  }, [stats, statusColors, t]);

  // Hàm helper để lấy màu theo trạng thái
  const getStatusColor = (status: OrderStatusEnum | ShippingStatusEnum): string => {
    return statusColors[status] || '#6b7280';
  };

  // Hàm helper để lấy label theo trạng thái
  const getStatusLabel = (status: OrderStatusEnum | ShippingStatusEnum): string => {
    const statusLabels = {
      // Order status labels
      [OrderStatusEnum.PENDING]: t('business:order.status.pending'),
      [OrderStatusEnum.CONFIRMED]: t('business:order.status.confirmed'),
      [OrderStatusEnum.PROCESSING]: t('business:order.status.processing'),
      [OrderStatusEnum.COMPLETED]: t('business:order.status.completed'),
      [OrderStatusEnum.CANCELLED]: t('business:order.status.cancelled'),
      
      // Shipping status labels
      [ShippingStatusEnum.PENDING]: t('business:order.shippingStatus.pending'),
      [ShippingStatusEnum.PREPARING]: t('business:order.shippingStatus.preparing'),
      [ShippingStatusEnum.SHIPPED]: t('business:order.shippingStatus.shipped'),
      [ShippingStatusEnum.IN_TRANSIT]: t('business:order.shippingStatus.inTransit'),
      [ShippingStatusEnum.SORTING]: t('business:order.shippingStatus.sorting'),
      [ShippingStatusEnum.DELIVERED]: t('business:order.shippingStatus.delivered'),
      [ShippingStatusEnum.DELIVERY_FAILED]: t('business:order.shippingStatus.deliveryFailed'),
      [ShippingStatusEnum.RETURNING]: t('business:order.shippingStatus.returning'),
      [ShippingStatusEnum.CANCELLED]: t('business:order.shippingStatus.cancelled'),
    };

    return statusLabels[status] || status;
  };

  // Hàm helper để kiểm tra trạng thái có phải là trạng thái hoàn thành không
  const isCompletedStatus = (status: OrderStatusEnum): boolean => {
    return status === OrderStatusEnum.COMPLETED;
  };

  // Hàm helper để kiểm tra trạng thái có phải là trạng thái đang xử lý không
  const isProcessingStatus = (status: OrderStatusEnum): boolean => {
    return [OrderStatusEnum.CONFIRMED, OrderStatusEnum.PROCESSING].includes(status);
  };

  // Hàm helper để kiểm tra trạng thái có phải là trạng thái bị hủy không
  const isCancelledStatus = (status: OrderStatusEnum): boolean => {
    return status === OrderStatusEnum.CANCELLED;
  };

  return {
    // Data
    stats,
    metrics,
    orderChartData,
    shippingChartData,
    
    // Loading states
    isLoading,
    error,
    
    // Helper functions
    getStatusColor,
    getStatusLabel,
    isCompletedStatus,
    isProcessingStatus,
    isCancelledStatus,
    
    // Constants
    statusColors,
  };
};

/**
 * Hook đơn giản chỉ để lấy thống kê cơ bản
 */
export const useOrderMetrics = () => {
  const { metrics, isLoading, error } = useOrderStatus();
  
  return {
    metrics,
    isLoading,
    error,
  };
};
