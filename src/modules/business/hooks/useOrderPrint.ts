import { useMutation, useQuery } from '@tanstack/react-query';
import { useTranslation } from 'react-i18next';
import { BusinessApiService } from '../services/business-api.service';
import { NotificationUtil } from '@/shared/utils/notification';
import { AxiosError } from 'axios';

/**
 * Interface cho tùy chọn in đơn hàng
 */
export interface PrintOrderOptions {
  paperSize?: 'A4' | 'A5' | 'A6';
  orientation?: 'portrait' | 'landscape';
  ghnFormat?: 'A5' | '80x80' | '52x70';
  includeBarcode?: boolean;
  includeQRCode?: boolean;
}

/**
 * Interface cho kết quả in đơn hàng
 */
export interface PrintOrderResult {
  orderId: string;
  carrier: string;
  trackingNumber: string;
  printType: 'token' | 'pdf' | 'url';
  printData: {
    token?: string;
    printUrl?: string;
    pdfUrl?: string;
    instructions?: string;
    format?: string;
  };
  createdAt: number;
}

/**
 * Hook in đơn hàng
 */
export const useOrderPrint = () => {
  const { t } = useTranslation(['business', 'common']);

  return useMutation({
    mutationFn: async ({ 
      orderId, 
      options = {} 
    }: { 
      orderId: number; 
      options?: PrintOrderOptions 
    }): Promise<PrintOrderResult> => {
      try {
        console.log('🖨️ [useOrderPrint] Printing order:', orderId, 'with options:', options);

        // Gọi API in đơn hàng từ BusinessApiService
        const result = await BusinessApiService.printOrder(orderId, options);

        console.log('✅ [useOrderPrint] Print result:', result);

        return result;
      } catch (error: unknown) {
        console.error('❌ [useOrderPrint] Print error:', error);
        throw error;
      }
    },
    onSuccess: (result) => {
      NotificationUtil.success({
        title: t('business:order.printSuccess'),
        message: t('business:order.printSuccessDescription'),
        duration: 5000,
      });

      // Nếu có URL in, mở trong tab mới
      if (result.printData.printUrl) {
        window.open(result.printData.printUrl, '_blank');
      }
    },
    onError: (error: AxiosError<{ message: string }>) => {
      console.error('❌ [useOrderPrint] Print failed:', error);
      
      NotificationUtil.error({
        title: t('business:order.printError'),
        message: error.response?.data?.message || t('business:order.printErrorDescription'),
        duration: 5000,
      });
    },
  });
};

/**
 * Hook lấy danh sách đơn hàng có thể in
 */
export const usePrintableOrders = (params?: { 
  hasShipping?: boolean;
  shippingStatus?: string[];
}) => {
  const { t } = useTranslation(['business', 'common']);

  return useQuery({
    queryKey: ['business', 'orders', 'printable', params],
    queryFn: async () => {
      // Lấy danh sách đơn hàng có thể in (có tracking number)
      const result = await BusinessApiService.getOrders({
        ...params,
        // Chỉ lấy đơn hàng có vận chuyển
        // hasShipping: true,
      });

      // Filter các đơn hàng có thể in (có tracking number)
      const printableOrders = result.items.filter(order => {
        const logisticInfo = order.logisticInfo as { trackingNumber?: string; shippingProvider?: string };
        return logisticInfo?.trackingNumber && logisticInfo?.shippingProvider;
      });

      return {
        ...result,
        items: printableOrders,
      };
    },
    refetchOnWindowFocus: false,
    staleTime: 300000, // 5 minutes
    onError: (error: AxiosError<{ message: string }>) => {
      NotificationUtil.error({
        message: error.response?.data?.message || t('business:order.fetchPrintableOrdersError'),
        duration: 3000,
      });
    },
  });
};

/**
 * Hook in hàng loạt đơn hàng
 */
export const useBatchOrderPrint = () => {
  const { t } = useTranslation(['business', 'common']);

  return useMutation({
    mutationFn: async ({ 
      orderIds, 
      options = {} 
    }: { 
      orderIds: number[]; 
      options?: PrintOrderOptions 
    }): Promise<PrintOrderResult[]> => {
      try {
        console.log('🖨️ [useBatchOrderPrint] Printing orders:', orderIds, 'with options:', options);

        // In từng đơn hàng một cách tuần tự
        const results: PrintOrderResult[] = [];
        
        for (const orderId of orderIds) {
          try {
            const result = await BusinessApiService.printOrder(orderId, options);
            results.push(result);
          } catch (error) {
            console.error(`❌ [useBatchOrderPrint] Failed to print order ${orderId}:`, error);
            // Tiếp tục in các đơn hàng khác
          }
        }

        console.log('✅ [useBatchOrderPrint] Batch print results:', results);

        return results;
      } catch (error: unknown) {
        console.error('❌ [useBatchOrderPrint] Batch print error:', error);
        throw error;
      }
    },
    onSuccess: (results) => {
      const successCount = results.length;
      
      NotificationUtil.success({
        title: t('business:order.batchPrintSuccess'),
        message: t('business:order.batchPrintSuccessDescription', { count: successCount }),
        duration: 5000,
      });

      // Mở tất cả URL in trong tab mới
      results.forEach(result => {
        if (result.printData.printUrl) {
          window.open(result.printData.printUrl, '_blank');
        }
      });
    },
    onError: (error: AxiosError<{ message: string }>) => {
      console.error('❌ [useBatchOrderPrint] Batch print failed:', error);
      
      NotificationUtil.error({
        title: t('business:order.batchPrintError'),
        message: error.response?.data?.message || t('business:order.batchPrintErrorDescription'),
        duration: 5000,
      });
    },
  });
};

/**
 * Hook kiểm tra đơn hàng có thể in không
 */
export const useCanPrintOrder = (orderId: number) => {
  return useQuery({
    queryKey: ['business', 'orders', orderId, 'can-print'],
    queryFn: async () => {
      try {
        // Lấy chi tiết đơn hàng
        const order = await BusinessApiService.getOrderDetail(orderId);
        
        // Kiểm tra điều kiện có thể in
        const logisticInfo = order.logisticInfo as { trackingNumber?: string; shippingProvider?: string };
        const canPrint = !!(
          order.hasShipping && 
          logisticInfo?.trackingNumber && 
          logisticInfo?.shippingProvider
        );

        return {
          canPrint,
          reason: canPrint ? null : 'Đơn hàng chưa có thông tin vận chuyển',
          carrier: logisticInfo?.shippingProvider,
          trackingNumber: logisticInfo?.trackingNumber,
        };
      } catch {
        return {
          canPrint: false,
          reason: 'Không thể kiểm tra thông tin đơn hàng',
          carrier: null,
          trackingNumber: null,
        };
      }
    },
    enabled: !!orderId,
    refetchOnWindowFocus: false,
    staleTime: 60000, // 1 minute
  });
};

/**
 * Hook lấy lịch sử in đơn hàng
 */
export const useOrderPrintHistory = (orderId: number) => {
  return useQuery({
    queryKey: ['business', 'orders', orderId, 'print-history'],
    queryFn: async () => {
      // TODO: Implement API to get print history
      // For now, return empty array
      return [];
    },
    enabled: !!orderId,
    refetchOnWindowFocus: false,
    staleTime: 300000, // 5 minutes
  });
};
