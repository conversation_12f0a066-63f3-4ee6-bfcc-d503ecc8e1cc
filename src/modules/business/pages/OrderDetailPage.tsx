import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Button,
  Icon,
  Loading,
} from '@/shared/components/common';
import { useOrder } from '../hooks/useOrderQuery';
import { formatTimestamp } from '@/shared/utils/date';
import { formatCurrency } from '@/shared/utils/format';

/**
 * Trang chi tiết đơn hàng
 */
const OrderDetailPage: React.FC = () => {
  const { t } = useTranslation(['business', 'common']);
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const orderId = id ? parseInt(id) : 0;

  // Lấy chi tiết đơn hàng
  const { data: order, isLoading, error } = useOrder(orderId);

  console.log('🔍 [OrderDetailPage] Order ID:', orderId);
  console.log('🔍 [OrderDetailPage] Order data:', order);
  console.log('🔍 [OrderDetailPage] Loading:', isLoading);
  console.log('🔍 [OrderDetailPage] Error:', error);

  // Xử lý loading
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Loading size="lg" />
          <p className="mt-4 text-gray-600">Đang tải chi tiết đơn hàng...</p>
        </div>
      </div>
    );
  }

  // Xử lý lỗi
  if (error || !order) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <div className="max-w-md w-full bg-white rounded-xl shadow-lg p-8 text-center border border-gray-200">
          <div className="w-16 h-16 mx-auto mb-6 bg-red-100 rounded-full flex items-center justify-center">
            <Icon name="alert-circle" size="lg" className="text-red-600" />
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-3">
            Không tìm thấy đơn hàng
          </h2>
          <p className="text-gray-600 mb-8 leading-relaxed">
            Đơn hàng không tồn tại hoặc bạn không có quyền xem.
          </p>
          <button
            onClick={() => navigate('/business/order')}
            className="w-full bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition-colors font-medium"
          >
            Quay lại danh sách
          </button>
        </div>
      </div>
    );
  }

  // Helper functions để render status
  const getOrderStatusLabel = (status: string) => {
    const statusMap: Record<string, string> = {
      'pending': 'Chờ xử lý',
      'processing': 'Đang xử lý',
      'completed': 'Hoàn thành',
      'cancelled': 'Đã hủy',
    };
    return statusMap[status] || status;
  };

  const getShippingStatusLabel = (status: string) => {
    const statusMap: Record<string, string> = {
      'pending': 'Chờ vận chuyển',
      'preparing': 'Chuẩn bị hàng',
      'shipped': 'Đã giao vận',
      'delivered': 'Đã giao hàng',
      'cancelled': 'Đã hủy',
    };
    return statusMap[status] || status;
  };

  const getPaymentStatusLabel = (status: string) => {
    const statusMap: Record<string, string> = {
      'PENDING': 'Chờ thanh toán',
      'PAID': 'Đã thanh toán',
      'UNPAID': 'Chưa thanh toán',
      'PARTIALLY_PAID': 'Thanh toán một phần',
    };
    return statusMap[status] || status;
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              onClick={() => navigate('/business/order')}
              className="p-2"
            >
              <Icon name="arrow-left" size="sm" />
            </Button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                Chi tiết đơn hàng #{order.id}
              </h1>
              <p className="text-gray-600">
                Ngày tạo: {formatTimestamp(parseInt(order.createdAt))}
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <span className="px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-700">
              {getOrderStatusLabel(order.orderStatus)}
            </span>
            {order.hasShipping && (
              <span className="px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-700">
                {getShippingStatusLabel(order.shippingStatus)}
              </span>
            )}
          </div>
        </div>

        {/* Thông tin sản phẩm */}
        <Card className="bg-white shadow-sm border border-gray-200 rounded-lg">
          <div className="p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Thông tin sản phẩm</h2>
            {order.productInfo?.products?.map((product: any, index: number) => (
              <div key={index} className="flex items-center justify-between p-4 border rounded-lg mb-4 last:mb-0">
                <div className="flex-1">
                  <h4 className="font-medium text-gray-900">{product.name}</h4>
                  <p className="text-sm text-gray-600">
                    Số lượng: {product.quantity} | Đơn giá: {formatCurrency(product.unitPrice)}
                  </p>
                  {product.description && (
                    <p className="text-sm text-gray-500 mt-1">{product.description}</p>
                  )}
                </div>
                <div className="text-right">
                  <p className="font-semibold text-gray-900">
                    {formatCurrency(product.totalPrice)}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </Card>

        {/* Thông tin thanh toán */}
        <Card className="bg-white shadow-sm border border-gray-200 rounded-lg">
          <div className="p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Thông tin thanh toán</h2>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">Tạm tính:</span>
                <span className="font-medium">{formatCurrency(order.billInfo?.subtotal || 0)}</span>
              </div>
              {order.billInfo?.shippingFee && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Phí vận chuyển:</span>
                  <span className="font-medium">{formatCurrency(order.billInfo.shippingFee)}</span>
                </div>
              )}
              <div className="flex justify-between border-t pt-3">
                <span className="text-lg font-semibold">Tổng cộng:</span>
                <span className="text-lg font-bold text-blue-600">
                  {formatCurrency(order.billInfo?.total || 0)}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Phương thức thanh toán:</span>
                <span className="font-medium">{order.billInfo?.paymentMethod || 'N/A'}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Trạng thái thanh toán:</span>
                <span className="px-2 py-1 rounded text-sm font-medium bg-yellow-100 text-yellow-700">
                  {getPaymentStatusLabel(order.billInfo?.paymentStatus || 'PENDING')}
                </span>
              </div>
            </div>
          </div>
        </Card>

        {/* Thông tin vận chuyển */}
        {order.hasShipping && order.logisticInfo && (
          <Card className="bg-white shadow-sm border border-gray-200 rounded-lg">
            <div className="p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Thông tin vận chuyển</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-gray-600">Người nhận:</p>
                  <p className="font-medium">{order.logisticInfo.recipientName}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Số điện thoại:</p>
                  <p className="font-medium">{order.logisticInfo.recipientPhone}</p>
                </div>
                <div className="md:col-span-2">
                  <p className="text-sm text-gray-600">Địa chỉ giao hàng:</p>
                  <p className="font-medium">{order.logisticInfo.deliveryAddress}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Đơn vị vận chuyển:</p>
                  <p className="font-medium">{order.logisticInfo.carrier}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Mã vận đơn:</p>
                  <p className="font-medium">{order.logisticInfo.trackingNumber}</p>
                </div>
                {order.logisticInfo.estimatedDeliveryTime && (
                  <div className="md:col-span-2">
                    <p className="text-sm text-gray-600">Thời gian giao hàng dự kiến:</p>
                    <p className="font-medium">
                      {new Date(order.logisticInfo.estimatedDeliveryTime).toLocaleString('vi-VN')}
                    </p>
                  </div>
                )}
                {order.logisticInfo.shippingNote && (
                  <div className="md:col-span-2">
                    <p className="text-sm text-gray-600">Ghi chú vận chuyển:</p>
                    <p className="font-medium">{order.logisticInfo.shippingNote}</p>
                  </div>
                )}
              </div>
            </div>
          </Card>
        )}

        {/* Actions */}
        <div className="flex justify-end space-x-2">
          <Button
            variant="outline"
            onClick={() => {
              console.log('Print order:', order.id);
              // TODO: Implement print functionality
            }}
          >
            <Icon name="printer" size="sm" className="mr-2" />
            In đơn hàng
          </Button>
        </div>
      </div>
    </div>
  );
};

export default OrderDetailPage;
