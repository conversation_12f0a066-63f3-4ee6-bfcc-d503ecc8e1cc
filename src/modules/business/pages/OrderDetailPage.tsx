import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Button,
  Icon,
  Loading,
} from '@/shared/components/common';
import { useOrder } from '../hooks/useOrderQuery';
import { formatTimestamp } from '@/shared/utils/date';
import { formatCurrency } from '@/shared/utils/format';

/**
 * Trang chi tiết đơn hàng
 */
const OrderDetailPage: React.FC = () => {
  const { t } = useTranslation(['business', 'common']);
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const orderId = id ? parseInt(id) : 0;

  // Lấy chi tiết đơn hàng
  const { data: order, isLoading, error } = useOrder(orderId);

  // Xử lý loading
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Loading size="lg" />
          <p className="mt-4 text-gray-600"><PERSON><PERSON> tải chi tiết đơn hàng...</p>
        </div>
      </div>
    );
  }

  // Xử lý lỗi
  if (error || !order) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <div className="max-w-md w-full bg-white rounded-xl shadow-lg p-8 text-center border border-gray-200">
          <div className="w-16 h-16 mx-auto mb-6 bg-red-100 rounded-full flex items-center justify-center">
            <Icon name="alert-circle" size="lg" className="text-red-600" />
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-3">
            Không tìm thấy đơn hàng
          </h2>
          <p className="text-gray-600 mb-8 leading-relaxed">
            Đơn hàng không tồn tại hoặc bạn không có quyền xem.
          </p>
          <button
            onClick={() => navigate('/business/order')}
            className="w-full bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition-colors font-medium"
          >
            Quay lại danh sách
          </button>
        </div>
      </div>
    );
  }

  // Render trạng thái đơn hàng
  const renderOrderStatus = (status: OrderStatusEnum) => {
    const statusConfig = {
      [OrderStatusEnum.PENDING]: { variant: 'warning', label: t('business:order.status.pending') },
      [OrderStatusEnum.CONFIRMED]: { variant: 'info', label: t('business:order.status.confirmed') },
      [OrderStatusEnum.PROCESSING]: { variant: 'info', label: t('business:order.status.processing') },
      [OrderStatusEnum.COMPLETED]: { variant: 'success', label: t('business:order.status.completed') },
      [OrderStatusEnum.CANCELLED]: { variant: 'danger', label: t('business:order.status.cancelled') },
    };
    const config = statusConfig[status] || { variant: 'default', label: status };
    return <Chip variant={config.variant as 'warning' | 'info' | 'success' | 'danger' | 'default'}>{config.label}</Chip>;
  };

  // Render trạng thái vận chuyển
  const renderShippingStatus = (status: ShippingStatusEnum) => {
    const statusConfig = {
      [ShippingStatusEnum.PENDING]: { variant: 'warning', label: t('business:order.shippingStatus.pending') },
      [ShippingStatusEnum.PREPARING]: { variant: 'info', label: t('business:order.shippingStatus.preparing') },
      [ShippingStatusEnum.SHIPPED]: { variant: 'info', label: t('business:order.shippingStatus.shipped') },
      [ShippingStatusEnum.IN_TRANSIT]: { variant: 'info', label: t('business:order.shippingStatus.inTransit') },
      [ShippingStatusEnum.SORTING]: { variant: 'info', label: t('business:order.shippingStatus.sorting') },
      [ShippingStatusEnum.DELIVERED]: { variant: 'success', label: t('business:order.shippingStatus.delivered') },
      [ShippingStatusEnum.DELIVERY_FAILED]: { variant: 'danger', label: t('business:order.shippingStatus.deliveryFailed') },
      [ShippingStatusEnum.RETURNING]: { variant: 'warning', label: t('business:order.shippingStatus.returning') },
      [ShippingStatusEnum.CANCELLED]: { variant: 'danger', label: t('business:order.shippingStatus.cancelled') },
    };
    const config = statusConfig[status] || { variant: 'default', label: status };
    return <Chip variant={config.variant as 'warning' | 'info' | 'success' | 'danger' | 'default'}>{config.label}</Chip>;
  };

  // Render trạng thái thanh toán
  const renderPaymentStatus = (status: PaymentStatusEnum) => {
    const statusConfig = {
      [PaymentStatusEnum.PENDING]: { variant: 'warning', label: t('business:order.paymentStatus.pending') },
      [PaymentStatusEnum.PAID]: { variant: 'success', label: t('business:order.paymentStatus.paid') },
      [PaymentStatusEnum.FAILED]: { variant: 'danger', label: t('business:order.paymentStatus.failed') },
      [PaymentStatusEnum.REFUNDED]: { variant: 'info', label: t('business:order.paymentStatus.refunded') },
    };
    const config = statusConfig[status] || { variant: 'default', label: status };
    return <Chip variant={config.variant as 'warning' | 'info' | 'success' | 'danger' | 'default'}>{config.label}</Chip>;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            onClick={() => navigate('/business/order')}
            className="p-2"
          >
            <Icon name="arrow-left" size="sm" />
          </Button>
          <div>
            <h1 className="text-2xl font-bold">
              {t('business:order.detail')} #{order.id}
            </h1>
            <p className="text-gray-600">
              {t('common:createdAt')}: {formatTimestamp(order.createdAt)}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          {renderOrderStatus(order.orderStatus)}
          {order.hasShipping && renderShippingStatus(order.shippingStatus)}
        </div>
      </div>

      {/* Thông tin khách hàng */}
      <Card>
        <Card.Header>
          <Card.Title>{t('business:order.customerInfo')}</Card.Title>
        </Card.Header>
        <Card.Content>
          {order.userConvertCustomer ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium text-gray-700">
                  {t('business:customer.name')}
                </label>
                <p className="mt-1">{order.userConvertCustomer.name}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-700">
                  {t('business:customer.email')}
                </label>
                <p className="mt-1">{order.userConvertCustomer.email.primary}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-700">
                  {t('business:customer.phone')}
                </label>
                <p className="mt-1">{order.userConvertCustomer.phone}</p>
              </div>
              {order.userConvertCustomer.address && (
                <div>
                  <label className="text-sm font-medium text-gray-700">
                    {t('business:customer.address')}
                  </label>
                  <p className="mt-1">{order.userConvertCustomer.address}</p>
                </div>
              )}
            </div>
          ) : (
            <p className="text-gray-500">{t('business:order.noCustomerInfo')}</p>
          )}
        </Card.Content>
      </Card>

      {/* Thông tin sản phẩm */}
      <Card>
        <Card.Header>
          <Card.Title>{t('business:order.productInfo')}</Card.Title>
        </Card.Header>
        <Card.Content>
          {order.productInfo && (order.productInfo as { products?: unknown[] }).products ? (
            <div className="space-y-4">
              {((order.productInfo as { products: Array<{
                name: string;
                quantity: number;
                description?: string;
                unitPrice: number;
                totalPrice: number;
              }> }).products).map((product, index: number) => (
                <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex-1">
                    <h4 className="font-medium">{product.name}</h4>
                    <p className="text-sm text-gray-600">
                      {t('business:product.quantity')}: {product.quantity}
                    </p>
                    {product.description && (
                      <p className="text-sm text-gray-600 mt-1">{product.description}</p>
                    )}
                  </div>
                  <div className="text-right">
                    <p className="font-medium">{formatCurrency(product.unitPrice)}</p>
                    <p className="text-sm text-gray-600">
                      {t('business:order.total')}: {formatCurrency(product.totalPrice)}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-gray-500">{t('business:order.noProductInfo')}</p>
          )}
        </Card.Content>
      </Card>

      {/* Thông tin thanh toán */}
      <Card>
        <Card.Header>
          <Card.Title>{t('business:order.paymentInfo')}</Card.Title>
        </Card.Header>
        <Card.Content>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium text-gray-700">
                {t('business:order.paymentMethod')}
              </label>
              <p className="mt-1">{order.billInfo.paymentMethod}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-700">
                {t('business:order.paymentStatus.title')}
              </label>
              <div className="mt-1">
                {renderPaymentStatus(order.billInfo.paymentStatus)}
              </div>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-700">
                {t('business:order.subtotal')}
              </label>
              <p className="mt-1">{formatCurrency(order.billInfo.subtotal)}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-700">
                {t('business:order.shippingFee')}
              </label>
              <p className="mt-1">{formatCurrency(order.billInfo.shippingFee)}</p>
            </div>
            {order.billInfo.discount && (
              <div>
                <label className="text-sm font-medium text-gray-700">
                  {t('business:order.discount')}
                </label>
                <p className="mt-1 text-green-600">-{formatCurrency(order.billInfo.discount)}</p>
              </div>
            )}
            <div>
              <label className="text-sm font-medium text-gray-700">
                {t('business:order.total')}
              </label>
              <p className="mt-1 text-lg font-bold">{formatCurrency(order.billInfo.total)}</p>
            </div>
          </div>
        </Card.Content>
      </Card>

      {/* Thông tin vận chuyển */}
      {order.hasShipping && (
        <Card>
          <Card.Header>
            <Card.Title>{t('business:order.shippingInfo')}</Card.Title>
          </Card.Header>
          <Card.Content>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium text-gray-700">
                  {t('business:order.shippingStatus.title')}
                </label>
                <div className="mt-1">
                  {renderShippingStatus(order.shippingStatus)}
                </div>
              </div>
              {order.billInfo.selectedCarrier && (
                <div>
                  <label className="text-sm font-medium text-gray-700">
                    {t('business:order.carrier')}
                  </label>
                  <p className="mt-1">{order.billInfo.selectedCarrier}</p>
                </div>
              )}
              {order.logisticInfo && (order.logisticInfo as { deliveryAddress?: string }).deliveryAddress && (
                <div className="md:col-span-2">
                  <label className="text-sm font-medium text-gray-700">
                    {t('business:order.deliveryAddress')}
                  </label>
                  <p className="mt-1">{(order.logisticInfo as { deliveryAddress: string }).deliveryAddress}</p>
                </div>
              )}
            </div>
            
            {/* Tracking info */}
            {trackingInfo && (
              <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                <h4 className="font-medium mb-2">{t('business:order.trackingInfo')}</h4>
                <pre className="text-sm text-gray-600 whitespace-pre-wrap">
                  {JSON.stringify(trackingInfo, null, 2)}
                </pre>
              </div>
            )}
          </Card.Content>
        </Card>
      )}

      {/* Actions */}
      <div className="flex justify-end space-x-2">
        <Button
          variant="outline"
          onClick={() => navigate(`/business/order/${order.id}/edit`)}
        >
          <Icon name="edit" size="sm" className="mr-2" />
          {t('common:edit')}
        </Button>
        <Button
          variant="outline"
          onClick={() => {
            // TODO: Implement print functionality
            console.log('Print order:', order.id);
          }}
        >
          <Icon name="printer" size="sm" className="mr-2" />
          {t('business:order.print')}
        </Button>
      </div>
    </div>
  );
};

export default OrderDetailPage;
