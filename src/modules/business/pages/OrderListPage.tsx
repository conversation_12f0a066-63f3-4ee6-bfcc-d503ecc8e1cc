import React, { useMemo, useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import {
  Card,
  Table,
  Chip,
  Button,
  Icon,
  SlideInForm,
} from '@/shared/components/common';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { ActiveFilters } from '@/modules/components/filters';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';
import { useActiveFilters } from '@/shared/hooks/filters';
import { useSlideForm } from '@/shared/hooks/useSlideForm';
import { useOrders, useCreateOrder, useOrderStatusStats } from '../hooks/useOrderQuery';
import { OrderStatus, OrderQueryParams, PaymentMethod } from '../services/order.service';
import { CreateEnhancedOrderDto, OrderStatusEnum, ShippingStatusEnum } from '../types/order.types';

// All imports above are used in this file
import { formatTimestamp } from '@/shared/utils/date';
import { formatCurrency } from '@/shared/utils/format';
import EnhancedOrderForm from '../components/forms/EnhancedOrderForm';
import { NotificationUtil } from '@/shared/utils/notification';

/**
 * Trang danh sách đơn hàng
 */
const   OrderListPage: React.FC = () => {
  const { t } = useTranslation(['business', 'common']);
  const navigate = useNavigate();
  const [selectedItems, setSelectedItems] = useState<number[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Hook quản lý SlideInForm
  const {
    isVisible: isCreateFormVisible,
    showForm: showCreateForm,
    hideForm: hideCreateForm,
  } = useSlideForm();

  // Hook tạo đơn hàng
  const createOrderMutation = useCreateOrder();

  // Cấu hình columns cho table
  const columns = useMemo(() => [
    {
      title: t('business:order.orderNumber'),
      dataIndex: 'orderNumber',
      key: 'orderNumber',
      sortable: true,
      render: (value: unknown, record: { id: string }) => {
        const orderNumber = value as string;
        return (
          <Button
            variant="ghost"
            onClick={() => navigate(`/business/order/${record.id}`)}
            className="p-0 h-auto font-medium text-primary hover:text-primary-dark"
          >
            {orderNumber}
          </Button>
        );
      },
    },
    {
      title: t('business:order.customerName'),
      dataIndex: 'customerName',
      key: 'customerName',
      sortable: true,
    },
    {
      title: t('business:order.totalAmount'),
      dataIndex: 'totalAmount',
      key: 'totalAmount',
      sortable: true,
      render: (value: unknown) => formatCurrency(value as number),
    },
    {
      title: t('business:order.status.title'),
      dataIndex: 'status',
      key: 'status',
      render: (status: unknown) => {
        const orderStatus = status as OrderStatus;
        const statusConfig = {
          [OrderStatus.PENDING]: { color: 'text-yellow-600', label: t('business:order.status.pending') },
          [OrderStatus.PROCESSING]: { color: 'text-blue-600', label: t('business:order.status.processing') },
          [OrderStatus.COMPLETED]: { color: 'text-green-600', label: t('business:order.status.completed') },
          [OrderStatus.CANCELLED]: { color: 'text-red-600', label: t('business:order.status.cancelled') },
          [OrderStatus.REFUNDED]: { color: 'text-gray-600', label: t('business:order.status.refunded') },
        };
        const config = statusConfig[orderStatus] || { color: 'text-gray-600', label: orderStatus };
        return (
          <span className={`font-medium ${config.color}`}>
            {config.label}
          </span>
        );
      },
    },
    {
      title: t('business:order.paymentStatus.title'),
      dataIndex: 'paymentStatus',
      key: 'paymentStatus',
      render: (status: unknown) => {
        const paymentStatus = status as string;
        const statusConfig = {
          'PAID': { variant: 'success', label: t('business:order.paymentStatus.paid') },
          'UNPAID': { variant: 'danger', label: t('business:order.paymentStatus.unpaid') },
          'PARTIALLY_PAID': { variant: 'warning', label: t('business:order.paymentStatus.partiallyPaid') },
        };
        const config = statusConfig[paymentStatus as keyof typeof statusConfig] || { variant: 'default', label: paymentStatus };
        return <Chip size="sm" variant={config.variant as 'warning' | 'info' | 'success' | 'danger' | 'default'}>{config.label}</Chip>;
      },
    },
    {
      title: t('business:order.shippingService'),
      dataIndex: 'shippingStatus',
      key: 'shippingStatus',
      render: (status: unknown) => {
        const shippingStatus = status as ShippingStatusEnum;
        const statusConfig = {
          [ShippingStatusEnum.PENDING]: { variant: 'warning', label: t('business:order.shippingStatus.pending') },
          [ShippingStatusEnum.PREPARING]: { variant: 'info', label: t('business:order.shippingStatus.preparing') },
          [ShippingStatusEnum.SHIPPED]: { variant: 'info', label: t('business:order.shippingStatus.shipped') },
          [ShippingStatusEnum.IN_TRANSIT]: { variant: 'info', label: t('business:order.shippingStatus.inTransit') },
          [ShippingStatusEnum.SORTING]: { variant: 'info', label: t('business:order.shippingStatus.sorting') },
          [ShippingStatusEnum.DELIVERED]: { variant: 'success', label: t('business:order.shippingStatus.delivered') },
          [ShippingStatusEnum.DELIVERY_FAILED]: { variant: 'danger', label: t('business:order.shippingStatus.deliveryFailed') },
          [ShippingStatusEnum.RETURNING]: { variant: 'warning', label: t('business:order.shippingStatus.returning') },
          [ShippingStatusEnum.CANCELLED]: { variant: 'danger', label: t('business:order.shippingStatus.cancelled') },
        };
        const config = statusConfig[shippingStatus] || { variant: 'default', label: shippingStatus };
        return <Chip size="sm" variant={config.variant as 'warning' | 'info' | 'success' | 'danger' | 'default'}>{config.label}</Chip>;
      },
    },
    {
      title: t('common:createdAt'),
      dataIndex: 'createdAt',
      key: 'createdAt',
      sortable: true,
      render: (value: unknown) => formatTimestamp(value as number),
    },
    {
      title: t('common:actions'),
      key: 'actions',
      width: 120,
      render: (_: unknown, record: { id: string }) => (
        <div className="flex items-center gap-1">
          <Button
            size="sm"
            variant="ghost"
            onClick={() => navigate(`/business/order/${record.id}`)}
            title={t('common:view')}
          >
            <Icon name="eye" size="sm" />
          </Button>
          <Button
            size="sm"
            variant="ghost"
            onClick={() => navigate(`/business/order/${record.id}/edit`)}
            title={t('common:edit')}
          >
            <Icon name="edit" size="sm" />
          </Button>
          <Button
            size="sm"
            variant="ghost"
            onClick={() => handlePrintOrder(record.id)}
            title={t('business:order.print')}
          >
            <Icon name="printer" size="sm" />
          </Button>
        </div>
      ),
    },
  ], [t, navigate]);

  // Tạo hàm createQueryParams
  const createQueryParams = useCallback((params: {
    page: number;
    pageSize: number;
    searchTerm: string;
    sortBy: string | null;
    sortDirection: 'ASC' | 'DESC' | null;
    filterValue: string | number | boolean | undefined;
    dateRange: [Date | null, Date | null];
  }): OrderQueryParams => {
    const queryParams: OrderQueryParams = {
      page: params.page,
      limit: params.pageSize,
      search: params.searchTerm || undefined,
      sortBy: params.sortBy || undefined,
      sortDirection: params.sortDirection || undefined,
    };

    // Thêm filter nếu có
    if (params.filterValue && params.filterValue !== 'all') {
      queryParams.status = params.filterValue as OrderStatus;
    }

    // Xử lý dateRange nếu có
    if (params.dateRange[0] && params.dateRange[1]) {
      queryParams.fromDate = params.dateRange[0].toISOString().split('T')[0];
      queryParams.toDate = params.dateRange[1].toISOString().split('T')[0];
    }

    return queryParams;
  }, []);

  // Cấu hình data table
  const dataTable = useDataTable(useDataTableConfig({
    columns,
    createQueryParams,
  }));

  // Lấy dữ liệu đơn hàng
  const { data: ordersData, isLoading, error } = useOrders(dataTable.queryParams);

  // Debug logging
  console.log('🔍 [OrderListPage] Query params:', dataTable.queryParams);
  console.log('🔍 [OrderListPage] Orders data:', ordersData);
  console.log('🔍 [OrderListPage] Loading:', isLoading);
  console.log('🔍 [OrderListPage] Error:', error);



  // Check for permission error
  const isPermissionError = error && (error as any)?.response?.status === 400;
  const errorMessage = (error as any)?.response?.data?.message || '';
  const isNoPermission = isPermissionError && (
    errorMessage.includes('permission') ||
    errorMessage.includes('access') ||
    errorMessage.includes('unauthorized')
  );

  // Lấy thống kê trạng thái đơn hàng
  const { data: orderStats } = useOrderStatusStats();

  // Xử lý tạo đơn hàng mới
  const handleCreateOrder = () => {
    showCreateForm();
  };

  // Xử lý submit form tạo đơn hàng
  const handleSubmitCreateOrder = async (orderData: CreateEnhancedOrderDto) => {
    setIsSubmitting(true);
    try {
      console.log('Creating order with data:', orderData);

      // Transform data để phù hợp với API hiện tại
      const apiOrderData = {
        customerId: orderData.customer.id || 0, // Sẽ cần tạo customer trước nếu chưa có
        items: orderData.items.map(item => ({
          productId: item.productId,
          quantity: item.quantity,
          price: item.price,
          productType: item.productType, // Thêm thông tin loại sản phẩm
        })),
        paymentMethod: orderData.payment.method as PaymentMethod,
        notes: orderData.notes,
        // Thêm thông tin vận chuyển nếu có
        shipping: orderData.shipping ? {
          method: orderData.shipping.method,
          serviceId: orderData.shipping.serviceId,
          serviceName: orderData.shipping.serviceName,
          fromAddress: orderData.shipping.fromAddress,
          toAddress: orderData.shipping.toAddress,
          fee: orderData.shipping.fee,
          estimatedDelivery: orderData.shipping.estimatedDelivery,
          note: orderData.shipping.note,
        } : undefined,
        // Thêm thông tin giao hàng số nếu có
        digitalDelivery: orderData.digitalDelivery ? {
          method: orderData.digitalDelivery.method,
          recipient: orderData.digitalDelivery.recipient,
          message: orderData.digitalDelivery.message,
          scheduledDelivery: orderData.digitalDelivery.scheduledDelivery,
        } : undefined,
        // Thêm thông tin thanh toán chi tiết
        payment: {
          method: orderData.payment.method,
          status: orderData.payment.status,
          codAmount: orderData.payment.codAmount,
        },
        // Thêm tags nếu có
        tags: orderData.tags,
      };

      // Gọi API tạo đơn hàng
      const result = await createOrderMutation.mutateAsync(apiOrderData);

      console.log('Order created successfully:', result);

      // Hiển thị thông báo thành công
      NotificationUtil.success({
        title: t('business:order.createSuccess'),
        message: t('business:order.createSuccessDescription'),
        duration: 5000,
      });

      // Đóng form
      hideCreateForm();

    } catch (error) {
      console.error('Error creating order:', error);

      // Hiển thị thông báo lỗi
      NotificationUtil.error({
        title: t('business:order.createError'),
        message: t('business:order.createErrorDescription'),
        duration: 5000,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Xử lý hủy tạo đơn hàng
  const handleCancelCreateOrder = () => {
    hideCreateForm();
  };

  // Xử lý in đơn hàng
  const handlePrintOrder = (orderId: string) => {
    // TODO: Implement print functionality
    console.log('Print order:', orderId);
  };

  // Xử lý xóa đơn hàng
  const handleDeleteOrders = () => {
    // TODO: Implement delete functionality
    console.log('Delete orders:', selectedItems);
  };

  // Sử dụng useActiveFilters hook như trong ProductsPage
  const {
    handleClearSearch,
    handleClearFilter,
    handleClearSort,
    handleClearAll,
    getFilterLabel,
  } = useActiveFilters({
    handleSearch: dataTable.tableData.handleSearch,
    setSelectedFilterId: dataTable.filter.setSelectedId,
    setDateRange: dataTable.dateRange.setDateRange,
    handleSortChange: dataTable.tableData.handleSortChange,
    selectedFilterValue: dataTable.filter.selectedValue,
    filterValueLabelMap: {
      [OrderStatus.PENDING]: t('business:order.status.pending'),
      [OrderStatus.PROCESSING]: t('business:order.status.processing'),
      [OrderStatus.COMPLETED]: t('business:order.status.completed'),
      [OrderStatus.CANCELLED]: t('business:order.status.cancelled'),
      [OrderStatus.REFUNDED]: t('business:order.status.refunded'),
    },
    t,
  });

  // Cấu hình MenuIconBar items với thống kê
  const menuItems = [
    {
      id: 'all',
      label: `${t('common:all')} (${orderStats?.orderStatus.total || 0})`,
      icon: 'list' as const,
      onClick: () => dataTable.filter.setSelectedId('all'),
    },
    {
      id: 'pending',
      label: `${t('business:order.status.pending')} (${orderStats?.orderStatus.pending || 0})`,
      icon: 'clock' as const,
      onClick: () => dataTable.filter.setSelectedId(OrderStatusEnum.PENDING),
    },
    {
      id: 'confirmed',
      label: `${t('business:order.status.confirmed')} (${orderStats?.orderStatus.confirmed || 0})`,
      icon: 'check-circle' as const,
      onClick: () => dataTable.filter.setSelectedId(OrderStatusEnum.CONFIRMED),
    },
    {
      id: 'processing',
      label: `${t('business:order.status.processing')} (${orderStats?.orderStatus.processing || 0})`,
      icon: 'cog' as const,
      onClick: () => dataTable.filter.setSelectedId(OrderStatusEnum.PROCESSING),
    },
    {
      id: 'completed',
      label: `${t('business:order.status.completed')} (${orderStats?.orderStatus.completed || 0})`,
      icon: 'check' as const,
      onClick: () => dataTable.filter.setSelectedId(OrderStatusEnum.COMPLETED),
    },
    {
      id: 'cancelled',
      label: `${t('business:order.status.cancelled')} (${orderStats?.orderStatus.cancelled || 0})`,
      icon: 'x' as const,
      onClick: () => dataTable.filter.setSelectedId(OrderStatusEnum.CANCELLED),
    },
  ];

  // Show permission error if user doesn't have access
  if (isNoPermission) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[400px] space-y-4">
        <div className="text-center">
          <Icon name="lock" size="xl" className="mx-auto text-gray-400 mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            {t('business:order.noPermission', 'Không có quyền truy cập')}
          </h2>
          <p className="text-gray-600 max-w-md">
            {t('business:order.noPermissionDescription', 'Bạn không có quyền xem danh sách đơn hàng. Vui lòng liên hệ quản trị viên để được cấp quyền.')}
          </p>
          <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <p className="text-sm text-yellow-800">
              <strong>Error:</strong> {errorMessage || 'Permission denied'}
            </p>
            <p className="text-sm text-yellow-800 mt-1">
              <strong>Status:</strong> {(error as any)?.response?.status || 'Unknown'}
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div>
      <MenuIconBar
        onSearch={dataTable.tableData.handleSearch}
        onAdd={handleCreateOrder}
        items={menuItems}
        onDateRangeChange={dataTable.dateRange.setDateRange}
        onColumnVisibilityChange={dataTable.columnVisibility.setVisibleColumns}
        columns={dataTable.columnVisibility.visibleColumns}
        showDateFilter={true}
        showColumnFilter={true}
        additionalIcons={[
          {
            icon: 'trash',
            tooltip: t('common:bulkDelete'),
            variant: 'primary',
            onClick: handleDeleteOrders,
            className: 'text-red-500',
            condition: selectedItems.length > 0,
          },
        ]}
      />

      <ActiveFilters
        searchTerm={dataTable.tableData.searchTerm}
        onClearSearch={handleClearSearch}
        filterValue={dataTable.filter.selectedValue}
        filterLabel={getFilterLabel()}
        onClearFilter={handleClearFilter}
        dateRange={dataTable.dateRange.dateRange}
        onClearDateRange={() => dataTable.dateRange.setDateRange([null, null])}
        sortBy={dataTable.tableData.sortBy}
        sortDirection={dataTable.tableData.sortDirection}
        onClearSort={handleClearSort}
        onClearAll={handleClearAll}
      />

      {/* SlideInForm cho tạo đơn hàng */}
      <SlideInForm isVisible={isCreateFormVisible}>
        <EnhancedOrderForm
          onSubmit={handleSubmitCreateOrder}
          onCancel={handleCancelCreateOrder}
          isSubmitting={isSubmitting}
        />
      </SlideInForm>

      <Card className="overflow-hidden">

        <Table
          columns={dataTable.columnVisibility.visibleTableColumns}
          data={ordersData?.items || []}
          rowKey="id"
          loading={isLoading}
          sortable={true}
          selectable={true}
          rowSelection={{
            selectedRowKeys: selectedItems,
            onChange: keys => setSelectedItems(keys as number[]),
          }}
          onSortChange={dataTable.tableData.handleSortChange}
          pagination={{
            current: ordersData?.meta.currentPage || 1,
            pageSize: dataTable.tableData.pageSize,
            total: ordersData?.meta.totalItems || 0,
            onChange: dataTable.tableData.handlePageChange,
            showSizeChanger: true,
            pageSizeOptions: [10, 20, 50, 100],
            showFirstLastButtons: true,
            showPageInfo: true,
          }}
          emptyText={
            error
              ? t('business:order.loadError')
              : !isLoading && (!ordersData?.items || ordersData.items.length === 0)
                ? t('business:order.noOrders')
                : undefined
          }
        />
      </Card>
    </div>
  );
};

export default OrderListPage;
