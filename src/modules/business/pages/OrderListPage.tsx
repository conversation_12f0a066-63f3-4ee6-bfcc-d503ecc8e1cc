import React, { useMemo, useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { useAppSelector } from '@/shared/store';
import {
  Card,
  Table,
  Chip,
  Button,
  Icon,
  SlideInForm,
} from '@/shared/components/common';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { ActiveFilters } from '@/modules/components/filters';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';
import { useActiveFilters } from '@/shared/hooks/filters';
import { useSlideForm } from '@/shared/hooks/useSlideForm';
import { useOrders, useCreateOrder, useOrderStatusStats } from '../hooks/useOrderQuery';
import { OrderStatus, OrderQueryParams, PaymentMethod } from '../services/order.service';
import { CreateEnhancedOrderDto, OrderStatusEnum, ShippingStatusEnum } from '../types/order.types';

// All imports above are used in this file
import { formatTimestamp } from '@/shared/utils/date';
import { formatCurrency } from '@/shared/utils/format';
import EnhancedOrderForm from '../components/forms/EnhancedOrderForm';
import { NotificationUtil } from '@/shared/utils/notification';

/**
 * Trang danh sách đơn hàng
 */
const   OrderListPage: React.FC = () => {
  const { t } = useTranslation(['business', 'common']);
  const navigate = useNavigate();
  const [selectedItems, setSelectedItems] = useState<number[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Get current user info from Redux store
  const authCommon = useAppSelector(state => state.authCommon);
  const currentUser = authCommon.user;
  const userPermissions = currentUser?.permissions || [];

  // Hook quản lý SlideInForm
  const {
    isVisible: isCreateFormVisible,
    showForm: showCreateForm,
    hideForm: hideCreateForm,
  } = useSlideForm();

  // Hook tạo đơn hàng
  const createOrderMutation = useCreateOrder();

  // Cấu hình columns cho table
  const columns = useMemo(() => [
    {
      title: t('business:order.orderNumber'),
      dataIndex: 'orderNumber',
      key: 'orderNumber',
      sortable: true,
      render: (value: unknown, record: { id: string }) => {
        const orderNumber = value as string;
        return (
          <Button
            variant="ghost"
            onClick={() => navigate(`/business/order/${record.id}`)}
            className="p-0 h-auto font-medium text-primary hover:text-primary-dark"
          >
            {orderNumber}
          </Button>
        );
      },
    },
    {
      title: t('business:order.customerName'),
      dataIndex: 'customerName',
      key: 'customerName',
      sortable: true,
    },
    {
      title: t('business:order.totalAmount'),
      dataIndex: 'totalAmount',
      key: 'totalAmount',
      sortable: true,
      render: (value: unknown) => formatCurrency(value as number),
    },
    {
      title: t('business:order.status.title'),
      dataIndex: 'status',
      key: 'status',
      render: (status: unknown) => {
        const orderStatus = status as OrderStatus;
        const statusConfig = {
          [OrderStatus.PENDING]: { variant: 'warning', label: t('business:order.status.pending') },
          [OrderStatus.PROCESSING]: { variant: 'info', label: t('business:order.status.processing') },
          [OrderStatus.COMPLETED]: { variant: 'success', label: t('business:order.status.completed') },
          [OrderStatus.CANCELLED]: { variant: 'danger', label: t('business:order.status.cancelled') },
          [OrderStatus.REFUNDED]: { variant: 'default', label: t('business:order.status.refunded') },
        };
        const config = statusConfig[orderStatus] || { variant: 'default', label: orderStatus };
        return <Chip size="sm" variant={config.variant as 'warning' | 'info' | 'success' | 'danger' | 'default'}>{config.label}</Chip>;
      },
    },
    {
      title: t('business:order.paymentStatus.title'),
      dataIndex: 'paymentStatus',
      key: 'paymentStatus',
      render: (status: unknown) => {
        const paymentStatus = status as string;
        const statusConfig = {
          'PAID': { variant: 'success', label: t('business:order.paymentStatus.paid') },
          'UNPAID': { variant: 'danger', label: t('business:order.paymentStatus.unpaid') },
          'PARTIALLY_PAID': { variant: 'warning', label: t('business:order.paymentStatus.partiallyPaid') },
        };
        const config = statusConfig[paymentStatus as keyof typeof statusConfig] || { variant: 'default', label: paymentStatus };
        return <Chip size="sm" variant={config.variant as 'warning' | 'info' | 'success' | 'danger' | 'default'}>{config.label}</Chip>;
      },
    },
    {
      title: t('business:order.shippingService'),
      dataIndex: 'shippingStatus',
      key: 'shippingStatus',
      render: (status: unknown) => {
        const shippingStatus = status as ShippingStatusEnum;
        const statusConfig = {
          [ShippingStatusEnum.PENDING]: { variant: 'warning', label: t('business:order.shippingStatus.pending') },
          [ShippingStatusEnum.PREPARING]: { variant: 'info', label: t('business:order.shippingStatus.preparing') },
          [ShippingStatusEnum.SHIPPED]: { variant: 'info', label: t('business:order.shippingStatus.shipped') },
          [ShippingStatusEnum.IN_TRANSIT]: { variant: 'info', label: t('business:order.shippingStatus.inTransit') },
          [ShippingStatusEnum.SORTING]: { variant: 'info', label: t('business:order.shippingStatus.sorting') },
          [ShippingStatusEnum.DELIVERED]: { variant: 'success', label: t('business:order.shippingStatus.delivered') },
          [ShippingStatusEnum.DELIVERY_FAILED]: { variant: 'danger', label: t('business:order.shippingStatus.deliveryFailed') },
          [ShippingStatusEnum.RETURNING]: { variant: 'warning', label: t('business:order.shippingStatus.returning') },
          [ShippingStatusEnum.CANCELLED]: { variant: 'danger', label: t('business:order.shippingStatus.cancelled') },
        };
        const config = statusConfig[shippingStatus] || { variant: 'default', label: shippingStatus };
        return <Chip size="sm" variant={config.variant as 'warning' | 'info' | 'success' | 'danger' | 'default'}>{config.label}</Chip>;
      },
    },
    {
      title: t('common:createdAt'),
      dataIndex: 'createdAt',
      key: 'createdAt',
      sortable: true,
      render: (value: unknown) => formatTimestamp(value as number),
    },
    {
      title: t('common:actions'),
      key: 'actions',
      width: 120,
      render: (_: unknown, record: { id: string }) => (
        <div className="flex items-center gap-1">
          <Button
            size="sm"
            variant="ghost"
            onClick={() => navigate(`/business/order/${record.id}`)}
            title={t('common:view')}
          >
            <Icon name="eye" size="sm" />
          </Button>
          <Button
            size="sm"
            variant="ghost"
            onClick={() => navigate(`/business/order/${record.id}/edit`)}
            title={t('common:edit')}
          >
            <Icon name="edit" size="sm" />
          </Button>
          <Button
            size="sm"
            variant="ghost"
            onClick={() => handlePrintOrder(record.id)}
            title={t('business:order.print')}
          >
            <Icon name="printer" size="sm" />
          </Button>
        </div>
      ),
    },
  ], [t, navigate]);

  // Tạo hàm createQueryParams
  const createQueryParams = useCallback((params: {
    page: number;
    pageSize: number;
    searchTerm: string;
    sortBy: string | null;
    sortDirection: 'ASC' | 'DESC' | null;
    filterValue: string | number | boolean | undefined;
    dateRange: [Date | null, Date | null];
  }): OrderQueryParams => {
    const queryParams: OrderQueryParams = {
      page: params.page,
      limit: params.pageSize,
      search: params.searchTerm || undefined,
      sortBy: params.sortBy || undefined,
      sortDirection: params.sortDirection || undefined,
    };

    // Thêm filter nếu có
    if (params.filterValue && params.filterValue !== 'all') {
      queryParams.status = params.filterValue as OrderStatus;
    }

    // Xử lý dateRange nếu có
    if (params.dateRange[0] && params.dateRange[1]) {
      queryParams.fromDate = params.dateRange[0].toISOString().split('T')[0];
      queryParams.toDate = params.dateRange[1].toISOString().split('T')[0];
    }

    return queryParams;
  }, []);

  // Cấu hình data table
  const dataTable = useDataTable(useDataTableConfig({
    columns,
    createQueryParams,
  }));

  // Lấy dữ liệu đơn hàng
  const { data: ordersData, isLoading, error } = useOrders(dataTable.queryParams);

  // Debug logging
  console.log('🔍 [OrderListPage] Query params:', dataTable.queryParams);
  console.log('🔍 [OrderListPage] Orders data:', ordersData);
  console.log('🔍 [OrderListPage] Loading:', isLoading);
  console.log('🔍 [OrderListPage] Error:', error);

  // Debug API and authentication info
  console.log('🔍 [OrderListPage] API Debug Info:', {
    apiUrl: import.meta.env.VITE_API_URL,
    fullApiUrl: `${import.meta.env.VITE_API_URL}/v1`,
    expectedOrdersEndpoint: `${import.meta.env.VITE_API_URL}/v1/user/orders`,
    hasToken: !!authCommon.accessToken,
    tokenPreview: authCommon.accessToken?.substring(0, 50) + '...',
    authType: authCommon.authType,
    isAuthenticated: authCommon.isAuthenticated,
    userId: currentUser?.id,
    userEmail: currentUser?.email,
    userRole: currentUser?.role,
    userPermissions: userPermissions,
    hasOrderPermissions: userPermissions.some(p =>
      p.includes('order') ||
      p.includes('business') ||
      p.includes('user_dashboard')
    ),
  });

  // Check for permission error
  const isPermissionError = error && ((error as any)?.response?.status === 400 || (error as any)?.response?.status === 403);
  const errorMessage = (error as any)?.response?.data?.message || '';
  const errorCode = (error as any)?.response?.data?.errorCode || '';
  const errorStatus = (error as any)?.response?.status || '';

  // More comprehensive permission error detection
  const isNoPermission = isPermissionError && (
    errorMessage.toLowerCase().includes('permission') ||
    errorMessage.toLowerCase().includes('access') ||
    errorMessage.toLowerCase().includes('unauthorized') ||
    errorMessage.toLowerCase().includes('forbidden') ||
    errorCode.includes('ACCESS_DENIED') ||
    errorCode.includes('PERMISSION') ||
    errorStatus === 403
  );

  // Debug permission information
  console.log('🔍 [OrderListPage] Permission check:', {
    isPermissionError,
    isNoPermission,
    errorMessage,
    errorCode,
    errorStatus,
    hasError: !!error
  });

  // Lấy thống kê trạng thái đơn hàng
  const { data: orderStats, error: statsError } = useOrderStatusStats();

  // Fallback stats when API fails
  const fallbackStats = {
    orderStatus: {
      total: 0,
      pending: 0,
      confirmed: 0,
      processing: 0,
      completed: 0,
      cancelled: 0,
    }
  };

  const safeOrderStats = orderStats || fallbackStats;

  // Xử lý tạo đơn hàng mới
  const handleCreateOrder = () => {
    showCreateForm();
  };

  // Xử lý submit form tạo đơn hàng
  const handleSubmitCreateOrder = async (orderData: CreateEnhancedOrderDto) => {
    setIsSubmitting(true);
    try {
      console.log('Creating order with data:', orderData);

      // Transform data để phù hợp với API hiện tại
      const apiOrderData = {
        customerId: orderData.customer.id || 0, // Sẽ cần tạo customer trước nếu chưa có
        items: orderData.items.map(item => ({
          productId: item.productId,
          quantity: item.quantity,
          price: item.price,
          productType: item.productType, // Thêm thông tin loại sản phẩm
        })),
        paymentMethod: orderData.payment.method as PaymentMethod,
        notes: orderData.notes,
        // Thêm thông tin vận chuyển nếu có
        shipping: orderData.shipping ? {
          method: orderData.shipping.method,
          serviceId: orderData.shipping.serviceId,
          serviceName: orderData.shipping.serviceName,
          fromAddress: orderData.shipping.fromAddress,
          toAddress: orderData.shipping.toAddress,
          fee: orderData.shipping.fee,
          estimatedDelivery: orderData.shipping.estimatedDelivery,
          note: orderData.shipping.note,
        } : undefined,
        // Thêm thông tin giao hàng số nếu có
        digitalDelivery: orderData.digitalDelivery ? {
          method: orderData.digitalDelivery.method,
          recipient: orderData.digitalDelivery.recipient,
          message: orderData.digitalDelivery.message,
          scheduledDelivery: orderData.digitalDelivery.scheduledDelivery,
        } : undefined,
        // Thêm thông tin thanh toán chi tiết
        payment: {
          method: orderData.payment.method,
          status: orderData.payment.status,
          codAmount: orderData.payment.codAmount,
        },
        // Thêm tags nếu có
        tags: orderData.tags,
      };

      // Gọi API tạo đơn hàng
      const result = await createOrderMutation.mutateAsync(apiOrderData);

      console.log('Order created successfully:', result);

      // Hiển thị thông báo thành công
      NotificationUtil.success({
        title: t('business:order.createSuccess'),
        message: t('business:order.createSuccessDescription'),
        duration: 5000,
      });

      // Đóng form
      hideCreateForm();

    } catch (error) {
      console.error('Error creating order:', error);

      // Hiển thị thông báo lỗi
      NotificationUtil.error({
        title: t('business:order.createError'),
        message: t('business:order.createErrorDescription'),
        duration: 5000,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Xử lý hủy tạo đơn hàng
  const handleCancelCreateOrder = () => {
    hideCreateForm();
  };

  // Xử lý in đơn hàng
  const handlePrintOrder = (orderId: string) => {
    // TODO: Implement print functionality
    console.log('Print order:', orderId);
  };

  // Xử lý xóa đơn hàng
  const handleDeleteOrders = () => {
    // TODO: Implement delete functionality
    console.log('Delete orders:', selectedItems);
  };

  // Sử dụng useActiveFilters hook như trong ProductsPage
  const {
    handleClearSearch,
    handleClearFilter,
    handleClearSort,
    handleClearAll,
    getFilterLabel,
  } = useActiveFilters({
    handleSearch: dataTable.tableData.handleSearch,
    setSelectedFilterId: dataTable.filter.setSelectedId,
    setDateRange: dataTable.dateRange.setDateRange,
    handleSortChange: dataTable.tableData.handleSortChange,
    selectedFilterValue: dataTable.filter.selectedValue,
    filterValueLabelMap: {
      [OrderStatus.PENDING]: t('business:order.status.pending'),
      [OrderStatus.PROCESSING]: t('business:order.status.processing'),
      [OrderStatus.COMPLETED]: t('business:order.status.completed'),
      [OrderStatus.CANCELLED]: t('business:order.status.cancelled'),
      [OrderStatus.REFUNDED]: t('business:order.status.refunded'),
    },
    t,
  });

  // Cấu hình MenuIconBar items với thống kê
  const menuItems = [
    {
      id: 'all',
      label: `${t('common:all')} (${safeOrderStats.orderStatus.total})`,
      icon: 'list' as const,
      onClick: () => dataTable.filter.setSelectedId('all'),
    },
    {
      id: 'pending',
      label: `${t('business:order.status.pending')} (${safeOrderStats.orderStatus.pending})`,
      icon: 'clock' as const,
      onClick: () => dataTable.filter.setSelectedId(OrderStatusEnum.PENDING),
    },
    {
      id: 'confirmed',
      label: `${t('business:order.status.confirmed')} (${safeOrderStats.orderStatus.confirmed})`,
      icon: 'check-circle' as const,
      onClick: () => dataTable.filter.setSelectedId(OrderStatusEnum.CONFIRMED),
    },
    {
      id: 'processing',
      label: `${t('business:order.status.processing')} (${safeOrderStats.orderStatus.processing})`,
      icon: 'cog' as const,
      onClick: () => dataTable.filter.setSelectedId(OrderStatusEnum.PROCESSING),
    },
    {
      id: 'completed',
      label: `${t('business:order.status.completed')} (${safeOrderStats.orderStatus.completed})`,
      icon: 'check' as const,
      onClick: () => dataTable.filter.setSelectedId(OrderStatusEnum.COMPLETED),
    },
    {
      id: 'cancelled',
      label: `${t('business:order.status.cancelled')} (${safeOrderStats.orderStatus.cancelled})`,
      icon: 'x' as const,
      onClick: () => dataTable.filter.setSelectedId(OrderStatusEnum.CANCELLED),
    },
  ];

  // Show permission error if user doesn't have access
  if (isNoPermission) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[400px] space-y-4">
        <div className="text-center max-w-2xl">
          <Icon name="lock" size="xl" className="mx-auto text-gray-400 mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            {t('business:order.noPermission', 'Không có quyền truy cập')}
          </h2>
          <p className="text-gray-600 max-w-md mx-auto mb-4">
            {t('business:order.noPermissionDescription', 'Bạn không có quyền xem danh sách đơn hàng. Vui lòng liên hệ quản trị viên để được cấp quyền.')}
          </p>

          {/* Error details */}
          <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg text-left">
            <h3 className="font-medium text-yellow-800 mb-2">Chi tiết lỗi:</h3>
            <div className="space-y-1 text-sm text-yellow-800">
              <p><strong>Message:</strong> {errorMessage || 'Permission denied'}</p>
              <p><strong>Status:</strong> {errorStatus || 'Unknown'}</p>
              <p><strong>Error Code:</strong> {errorCode || 'Unknown'}</p>
            </div>
          </div>

          {/* Current permissions info */}
          <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg text-left">
            <h3 className="font-medium text-blue-800 mb-2">Thông tin quyền hiện tại:</h3>
            <div className="text-sm text-blue-800">
              <p><strong>Quyền hiện tại của bạn:</strong></p>
              {userPermissions.length > 0 ? (
                <ul className="list-disc list-inside mt-1 mb-3 space-y-1">
                  {userPermissions.map((permission, index) => (
                    <li key={index}><code>{permission}</code></li>
                  ))}
                </ul>
              ) : (
                <p className="mt-1 mb-3 text-red-600">Không có quyền nào được cấp</p>
              )}

              <p><strong>Để xem đơn hàng, bạn cần có một trong các quyền sau:</strong></p>
              <ul className="list-disc list-inside mt-2 space-y-1">
                <li><code>order:view_list</code> - Xem danh sách đơn hàng</li>
                <li><code>business:order:read</code> - Đọc thông tin đơn hàng</li>
                <li><code>user:order:view</code> - Xem đơn hàng của người dùng</li>
                <li><code>user_dashboard:view_info</code> - Xem thông tin dashboard</li>
              </ul>
            </div>
          </div>

          {/* Debug API test buttons */}
          {process.env.NODE_ENV === 'development' && (
            <div className="mt-4 p-4 bg-gray-50 border border-gray-200 rounded-lg text-left">
              <h3 className="font-medium text-gray-800 mb-2">Debug API Test:</h3>
              <div className="space-x-2">
                <button
                  onClick={async () => {
                    try {
                      const response = await fetch(`${import.meta.env.VITE_API_URL}/v1/user/orders`, {
                        headers: {
                          'Authorization': `Bearer ${authCommon.accessToken}`,
                          'Content-Type': 'application/json',
                        }
                      });
                      const data = await response.json();
                      console.log('Direct API test result:', { status: response.status, data });
                      alert(`API Response: ${response.status} - ${JSON.stringify(data, null, 2)}`);
                    } catch (error) {
                      console.error('Direct API test error:', error);
                      alert(`API Error: ${error}`);
                    }
                  }}
                  className="px-3 py-1 bg-blue-500 text-white rounded text-sm"
                >
                  Test Orders API
                </button>
                <button
                  onClick={async () => {
                    try {
                      const response = await fetch(`${import.meta.env.VITE_API_URL}/v1/users/profile`, {
                        headers: {
                          'Authorization': `Bearer ${authCommon.accessToken}`,
                          'Content-Type': 'application/json',
                        }
                      });
                      const data = await response.json();
                      console.log('Profile API test result:', { status: response.status, data });
                      alert(`Profile API Response: ${response.status} - ${JSON.stringify(data, null, 2)}`);
                    } catch (error) {
                      console.error('Profile API test error:', error);
                      alert(`Profile API Error: ${error}`);
                    }
                  }}
                  className="px-3 py-1 bg-green-500 text-white rounded text-sm"
                >
                  Test Profile API
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    );
  }

  return (
    <div>
      <MenuIconBar
        onSearch={dataTable.tableData.handleSearch}
        onAdd={handleCreateOrder}
        items={menuItems}
        onDateRangeChange={dataTable.dateRange.setDateRange}
        onColumnVisibilityChange={dataTable.columnVisibility.setVisibleColumns}
        columns={dataTable.columnVisibility.visibleColumns}
        showDateFilter={true}
        showColumnFilter={true}
        additionalIcons={[
          {
            icon: 'trash',
            tooltip: t('common:bulkDelete'),
            variant: 'primary',
            onClick: handleDeleteOrders,
            className: 'text-red-500',
            condition: selectedItems.length > 0,
          },
        ]}
      />

      <ActiveFilters
        searchTerm={dataTable.tableData.searchTerm}
        onClearSearch={handleClearSearch}
        filterValue={dataTable.filter.selectedValue}
        filterLabel={getFilterLabel()}
        onClearFilter={handleClearFilter}
        dateRange={dataTable.dateRange.dateRange}
        onClearDateRange={() => dataTable.dateRange.setDateRange([null, null])}
        sortBy={dataTable.tableData.sortBy}
        sortDirection={dataTable.tableData.sortDirection}
        onClearSort={handleClearSort}
        onClearAll={handleClearAll}
      />

      {/* SlideInForm cho tạo đơn hàng */}
      <SlideInForm isVisible={isCreateFormVisible}>
        <EnhancedOrderForm
          onSubmit={handleSubmitCreateOrder}
          onCancel={handleCancelCreateOrder}
          isSubmitting={isSubmitting}
        />
      </SlideInForm>

      <Card className="overflow-hidden">
        {/* Debug info */}
        {process.env.NODE_ENV === 'development' && (
          <div className="p-4 bg-gray-100 text-xs">
            <p><strong>Debug Info:</strong></p>
            <p>Loading: {isLoading ? 'true' : 'false'}</p>
            <p>Error: {error ? 'true' : 'false'}</p>
            <p>Data: {ordersData ? `${ordersData.items?.length || 0} items` : 'null'}</p>
            <p>Query params: {JSON.stringify(dataTable.queryParams)}</p>
          </div>
        )}

        <Table
          columns={dataTable.columnVisibility.visibleTableColumns}
          data={ordersData?.items || []}
          rowKey="id"
          loading={isLoading}
          sortable={true}
          selectable={true}
          rowSelection={{
            selectedRowKeys: selectedItems,
            onChange: keys => setSelectedItems(keys as number[]),
          }}
          onSortChange={dataTable.tableData.handleSortChange}
          pagination={{
            current: ordersData?.meta.currentPage || 1,
            pageSize: dataTable.tableData.pageSize,
            total: ordersData?.meta.totalItems || 0,
            onChange: dataTable.tableData.handlePageChange,
            showSizeChanger: true,
            pageSizeOptions: [10, 20, 50, 100],
            showFirstLastButtons: true,
            showPageInfo: true,
          }}
          emptyText={
            error
              ? t('business:order.loadError')
              : !isLoading && (!ordersData?.items || ordersData.items.length === 0)
                ? t('business:order.noOrders')
                : undefined
          }
        />
      </Card>
    </div>
  );
};

export default OrderListPage;
